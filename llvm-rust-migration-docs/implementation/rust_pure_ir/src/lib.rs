//! Pure Rust LLVM IR Implementation
//! 
//! This module provides a complete memory-safe implementation of LLVM IR
//! infrastructure, demonstrating the viability of pure Rust compiler
//! infrastructure with zero unsafe code.

#![forbid(unsafe_code)]
#![warn(missing_docs)]

use std::collections::HashMap;
use std::rc::{Rc, Weak};
use std::sync::{Arc, RwLock};

/// Core IR context managing types and constants
pub struct Context {
    types: Arc<RwLock<HashMap<TypeKey, Rc<Type>>>>,
    constants: Arc<RwLock<HashMap<ConstantKey, Rc<Constant>>>>,
    integer_types: Arc<RwLock<HashMap<u32, Rc<Type>>>>,
}

impl Context {
    /// Create a new IR context
    pub fn new() -> Self {
        Self {
            types: Arc::new(RwLock::new(HashMap::new())),
            constants: Arc::new(RwLock::new(HashMap::new())),
            integer_types: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Get or create an integer type with specified bit width
    pub fn get_integer_type(&self, bits: u32) -> Rc<Type> {
        let mut cache = self.integer_types.write().unwrap();
        cache.entry(bits)
            .or_insert_with(|| Rc::new(Type::Integer { bits }))
            .clone()
    }
    
    /// Get or create a function type
    pub fn get_function_type(&self, ret: Rc<Type>, params: Vec<Rc<Type>>) -> Rc<Type> {
        let key = TypeKey::Function { 
            ret: ret.clone(), 
            params: params.clone() 
        };
        let mut cache = self.types.write().unwrap();
        cache.entry(key)
            .or_insert_with(|| Rc::new(Type::Function { 
                ret: Box::new((*ret).clone()), 
                params: params.iter().map(|t| (**t).clone()).collect(),
                varargs: false 
            }))
            .clone()
    }
}

/// LLVM Type system with complete type coverage
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Type {
    /// Void type
    Void,
    /// Integer type with bit width
    Integer { bits: u32 },
    /// Floating point type
    Float { kind: FloatKind },
    /// Pointer type with pointee and address space
    Pointer { pointee: Box<Type>, address_space: u32 },
    /// Array type with element type and count
    Array { element: Box<Type>, count: u64 },
    /// Struct type with fields and packing
    Struct { fields: Vec<Type>, packed: bool },
    /// Function type with parameters and return type
    Function { params: Vec<Type>, ret: Box<Type>, varargs: bool },
    /// Vector type with element type and count
    Vector { element: Box<Type>, count: u32 },
}

impl Type {
    /// Check if this is an integer type
    pub fn is_integer(&self) -> bool {
        matches!(self, Type::Integer { .. })
    }
    
    /// Check if this is an integer type with specific bit width
    pub fn is_integer_with_bits(&self, bits: u32) -> bool {
        matches!(self, Type::Integer { bits: b } if *b == bits)
    }
    
    /// Check if this is a floating point type
    pub fn is_float(&self) -> bool {
        matches!(self, Type::Float { .. })
    }
    
    /// Get the size of this type in bits
    pub fn get_size_in_bits(&self) -> u64 {
        match self {
            Type::Void => 0,
            Type::Integer { bits } => *bits as u64,
            Type::Float { kind } => kind.get_size_in_bits(),
            Type::Pointer { .. } => 64, // Assume 64-bit pointers
            Type::Array { element, count } => element.get_size_in_bits() * count,
            Type::Struct { fields, packed: _ } => {
                fields.iter().map(|f| f.get_size_in_bits()).sum()
            }
            Type::Function { .. } => 64, // Function pointer size
            Type::Vector { element, count } => element.get_size_in_bits() * (*count as u64),
        }
    }
    
    /// Get the alignment of this type
    pub fn get_alignment(&self) -> u32 {
        match self {
            Type::Integer { bits } => (*bits / 8).max(1),
            Type::Float { kind } => kind.get_alignment(),
            Type::Pointer { .. } => 8,
            Type::Array { element, .. } => element.get_alignment(),
            Type::Struct { fields, .. } => {
                fields.iter().map(|f| f.get_alignment()).max().unwrap_or(1)
            }
            _ => 1,
        }
    }
}

/// Floating point type kinds
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum FloatKind {
    /// 16-bit half precision
    Half,
    /// 32-bit single precision
    Float,
    /// 64-bit double precision
    Double,
    /// 80-bit extended precision
    X86_FP80,
    /// 128-bit quadruple precision
    FP128,
}

impl FloatKind {
    /// Get the size in bits for this float kind
    pub fn get_size_in_bits(&self) -> u64 {
        match self {
            FloatKind::Half => 16,
            FloatKind::Float => 32,
            FloatKind::Double => 64,
            FloatKind::X86_FP80 => 80,
            FloatKind::FP128 => 128,
        }
    }
    
    /// Get the alignment for this float kind
    pub fn get_alignment(&self) -> u32 {
        match self {
            FloatKind::Half => 2,
            FloatKind::Float => 4,
            FloatKind::Double => 8,
            FloatKind::X86_FP80 => 16,
            FloatKind::FP128 => 16,
        }
    }
}

/// Memory-safe Value representation
pub struct Value {
    ty: Rc<Type>,
    users: Arc<RwLock<Vec<Weak<dyn User>>>>,
    name: Option<String>,
    metadata: HashMap<String, Metadata>,
}

impl Value {
    /// Create a new value with the given type
    pub fn new(ty: Rc<Type>, name: Option<String>) -> Self {
        Self {
            ty,
            users: Arc::new(RwLock::new(Vec::new())),
            name,
            metadata: HashMap::new(),
        }
    }
    
    /// Get the type of this value
    pub fn get_type(&self) -> Rc<Type> {
        self.ty.clone()
    }
    
    /// Get the name of this value
    pub fn get_name(&self) -> Option<&str> {
        self.name.as_deref()
    }
    
    /// Replace all uses of this value with another value
    pub fn replace_all_uses_with(&self, new_value: Value) -> Result<(), IRError> {
        let users = self.users.read().map_err(|_| IRError::LockError)?;
        for user_weak in users.iter() {
            if let Some(user) = user_weak.upgrade() {
                user.replace_use_of(self, new_value.clone())?;
            }
        }
        Ok(())
    }
    
    /// Add a user to this value
    pub fn add_user(&self, user: Weak<dyn User>) -> Result<(), IRError> {
        let mut users = self.users.write().map_err(|_| IRError::LockError)?;
        users.push(user);
        Ok(())
    }
    
    /// Get the number of users
    pub fn get_num_users(&self) -> usize {
        self.users.read().unwrap().len()
    }
    
    /// Check if this value has any users
    pub fn has_users(&self) -> bool {
        self.get_num_users() > 0
    }
}

/// User trait for values that use other values
pub trait User: Send + Sync {
    /// Get all operands used by this user
    fn get_operands(&self) -> Vec<&Value>;
    
    /// Replace use of old value with new value
    fn replace_use_of(&mut self, old: &Value, new: Value) -> Result<(), IRError>;
    
    /// Get the number of operands
    fn get_num_operands(&self) -> usize {
        self.get_operands().len()
    }
}

/// Complete Constants implementation
#[derive(Debug, Clone)]
pub enum Constant {
    /// Integer constant
    Int { value: i64, ty: Rc<Type> },
    /// Floating point constant
    Float { value: f64, ty: Rc<Type> },
    /// Null pointer constant
    Null { ty: Rc<Type> },
    /// Undefined value constant
    Undef { ty: Rc<Type> },
    /// Array constant
    Array { elements: Vec<Rc<Constant>>, ty: Rc<Type> },
    /// Struct constant
    Struct { fields: Vec<Rc<Constant>>, ty: Rc<Type> },
    /// Global value reference
    GlobalValue { name: String, ty: Rc<Type> },
    /// Constant expression
    ConstantExpr { op: ConstantOpcode, operands: Vec<Rc<Constant>> },
}

impl Constant {
    /// Create an integer constant
    pub fn get_int(context: &Context, value: i64, bits: u32) -> Rc<Constant> {
        let ty = context.get_integer_type(bits);
        let key = ConstantKey::Int { value, bits };
        
        context.constants
            .write()
            .unwrap()
            .entry(key)
            .or_insert_with(|| Rc::new(Constant::Int { value, ty }))
            .clone()
    }
    
    /// Perform constant folding for binary operations
    pub fn fold_binary_op(&self, op: BinaryOpcode, rhs: &Constant) -> Option<Rc<Constant>> {
        match (self, rhs) {
            (Constant::Int { value: lhs_val, ty }, Constant::Int { value: rhs_val, .. }) => {
                let result = match op {
                    BinaryOpcode::Add => lhs_val.wrapping_add(*rhs_val),
                    BinaryOpcode::Sub => lhs_val.wrapping_sub(*rhs_val),
                    BinaryOpcode::Mul => lhs_val.wrapping_mul(*rhs_val),
                    BinaryOpcode::Div => lhs_val.checked_div(*rhs_val)?,
                    _ => return None,
                };
                Some(Rc::new(Constant::Int { value: result, ty: ty.clone() }))
            }
            _ => None,
        }
    }
}

/// Binary operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BinaryOpcode {
    Add, Sub, Mul, Div, Rem,
    Shl, LShr, AShr,
    And, Or, Xor,
}

/// Constant operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConstantOpcode {
    Add, Sub, Mul, Div,
    GetElementPtr,
    Trunc, ZExt, SExt,
    PtrToInt, IntToPtr,
    BitCast,
}

/// IR Error types
#[derive(Debug, Clone)]
pub enum IRError {
    TypeMismatch { expected: Rc<Type>, found: Rc<Type> },
    InvalidPosition,
    LockError,
    NoCurrentBlock,
    InvalidOperandType,
    ExpectedPointerType,
    ExpectedFunctionType,
    ExpectedBooleanType,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    InvalidFunctionType,
    InsertAfterTerminator,
    MultipleTerminators,
}

/// Type keys for caching
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum TypeKey {
    Function { ret: Rc<Type>, params: Vec<Rc<Type>> },
    Pointer { pointee: Rc<Type>, address_space: u32 },
    Array { element: Rc<Type>, count: u64 },
}

/// Constant keys for caching
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ConstantKey {
    Int { value: i64, bits: u32 },
    Float { value: u64, bits: u32 }, // Using u64 for bit representation
    Null { ty: Rc<Type> },
}

/// Metadata for debugging and optimization
#[derive(Debug, Clone)]
pub struct Metadata {
    pub kind: String,
    pub data: Vec<u8>,
}

/// Arithmetic flags for optimization
#[derive(Debug, Clone, Default)]
pub struct ArithmeticFlags {
    pub no_unsigned_wrap: bool,
    pub no_signed_wrap: bool,
    pub exact: bool,
}

/// Fast math flags for floating point operations
#[derive(Debug, Clone, Default)]
pub struct FastMathFlags {
    pub allow_reassoc: bool,
    pub no_nans: bool,
    pub no_infs: bool,
    pub no_signed_zeros: bool,
    pub allow_reciprocal: bool,
    pub allow_contract: bool,
    pub approx_func: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_context_creation() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        assert!(int32_ty.is_integer_with_bits(32));
    }
    
    #[test]
    fn test_value_creation() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let value = Value::new(int32_ty.clone(), Some("test".to_string()));
        
        assert_eq!(value.get_type(), int32_ty);
        assert_eq!(value.get_name(), Some("test"));
        assert!(!value.has_users());
    }
    
    #[test]
    fn test_constant_folding() {
        let context = Context::new();
        let c1 = Constant::get_int(&context, 10, 32);
        let c2 = Constant::get_int(&context, 20, 32);
        
        let result = c1.fold_binary_op(BinaryOpcode::Add, &c2).unwrap();
        if let Constant::Int { value, .. } = &*result {
            assert_eq!(*value, 30);
        } else {
            panic!("Expected integer constant");
        }
    }
}
